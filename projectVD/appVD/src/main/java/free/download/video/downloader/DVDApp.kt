package free.download.video.downloader

import CoroutineTask
import FirebaseRemoteConfigManager
import android.app.Application
import android.database.CursorWindow
import com.lib.analysis.firebase.manager.FirebaseCrashlyticsManager
import com.lib.analysis.firebase.manager.FirebaseEventLogger
import com.lib.analysis.firebase.manager.GoogleAdConfigure
import com.mandi.common.ad.AdmobFactory
import com.tiny.lib.web.view.sniffer.SnifferDelegate
import com.tiny.lib.web.view.sniffer.core.WebViewSafeInitializer
import com.tinypretty.component.DefaultComponents
import com.tinypretty.component.GlobalModule
import com.tinypretty.component.IEvent
import com.tinypretty.component.IGoogleAdConfigure
import com.tinypretty.component.IRemoteConfigure
import free.download.video.downloader.utils.DatabaseHelper
import free.download.video.downloader.utils.DatabaseManager
import free.download.video.downloader.utils.DatabaseMigrationHelper
import org.koin.android.ext.koin.androidContext
import org.koin.core.context.startKoin
import org.koin.dsl.bind
import org.koin.dsl.module
import java.lang.reflect.Field

class DVDApp : Application() {

    override fun onCreate() {
        super.onCreate()

        startKoin {
            androidContext(this@DVDApp)
            val appModule = module {
                try {
                    //todo replace=FirebaseCrashlyticsManager.init()
                    //todo replace=single { FirebaseEventLogger } bind (IEvent::class)
                    //todo replace=single { FirebaseRemoteConfigManager } bind (IRemoteConfigure::class)
                } catch (e: ClassNotFoundException) {
                }
                single { GoogleAdConfigure } bind (IGoogleAdConfigure::class)
            }
            modules(DefaultComponents.modules(), appModule)
        }
        // 安全初始化WebView环境
        try {
            WebViewSafeInitializer.initWebViewEnvironment(this)
        } catch (e: Exception) {
            // 记录WebView初始化失败的详细信息
            e.printStackTrace()
        }

        CoroutineTask("initInDVDApp").io().launch {
            GlobalModule.remoteConfig.init()
        }
        // 增加 CursorWindow 大小，解决数据库查询崩溃问题
        DatabaseHelper.increaseCursorWindowSize()

        // 检查并清理数据库，限制记录数量为1000条
        CoroutineTask("checkDatabases").io().launch {
            try {
                // 先进行数据库健康检查
                DatabaseMigrationHelper.checkDatabaseHealth()
                // 然后清理数据库
                DatabaseManager.checkAllDatabases()
            } catch (e: Exception) {
            }
        }
    }
}
