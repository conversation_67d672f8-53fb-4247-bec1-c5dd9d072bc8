# WebView初始化问题修复方案

## 问题描述

在应用运行时出现以下错误日志：

```
2025-05-25 23:32:41.144 MT-WebView...nitializer I  WebView environment initialized successfully
2025-05-25 23:32:41.148 MT-WebView...nitializer E  WebView environment initialization failed: Can't set data directory suffix: WebView already initialized
2025-05-25 23:32:41.797 MT-WebView...nitializer E  WebView is not available on this device
```

## 问题根本原因

**WebView被重复初始化导致的错误**：

1. **第一次初始化**：在 `DVDApp.onCreate()` 中调用 `WebViewSafeInitializer.initWebViewEnvironment(this)` 成功
2. **第二次初始化**：在 `SnifferDelegate` 的 companion object 中又调用了一次初始化
3. **关键问题**：`WebView.setDataDirectorySuffix()` 只能在WebView第一次使用前调用一次，重复调用会抛出异常

## 修复方案

### 1. 添加初始化状态管理

在 `WebViewSafeInitializer` 中添加了 `isEnvironmentInitialized` 标记：

```kotlin
private var isEnvironmentInitialized = false

fun initWebViewEnvironment(context: Context) {
    // 防止重复初始化
    if (isEnvironmentInitialized) {
        log.i { "WebView environment already initialized, skipping..." }
        return
    }
    // ... 初始化逻辑
    isEnvironmentInitialized = true
}
```

### 2. 移除重复初始化调用

移除了 `SnifferDelegate` 中的重复初始化：

```kotlin
companion object {
    // 移除重复的WebView初始化调用
    // WebView环境初始化应该只在Application中进行一次
}
```

### 3. 改进错误处理

在 `SnifferDelegate.createWebView()` 中添加了备用初始化检查：

```kotlin
// 检查WebView环境是否已初始化
if (!WebViewSafeInitializer.isEnvironmentInitialized()) {
    log.w { "WebView environment not initialized, attempting to initialize..." }
    WebViewSafeInitializer.initWebViewEnvironment(activity.applicationContext)
}
```

### 4. 增强日志记录

添加了更详细的日志记录，便于问题排查：

- 初始化状态日志
- 数据目录设置日志
- WebView创建成功/失败日志

## 修复效果

修复后的初始化流程：

1. **应用启动时**：只在 `DVDApp.onCreate()` 中初始化一次WebView环境
2. **重复调用保护**：后续的初始化调用会被自动忽略
3. **备用机制**：如果环境未初始化，在创建WebView时会尝试初始化
4. **错误恢复**：即使WebView不可用，也会提供友好的错误提示

## 测试验证

创建了单元测试 `WebViewSafeInitializerTest.kt` 来验证：

- 重复初始化保护机制
- 初始化状态管理
- WebView可用性检查

## 相关文件修改

1. `modules/libWebViewSniffer/src/main/java/com/tiny/lib/web/view/sniffer/core/WebViewSafeInitializer.kt`
   - 添加初始化状态管理
   - 改进错误处理和日志

2. `modules/libWebViewSniffer/src/main/java/com/tiny/lib/web/view/sniffer/SnifferDelegate.kt`
   - 移除重复初始化调用
   - 改进createWebView方法

3. `projectVD/appVD/src/main/java/free/download/video/downloader/DVDApp.kt`
   - 清理初始化代码
   - 改进异常处理

## 预期结果

修复后应该不再出现以下错误：

- ❌ `Can't set data directory suffix: WebView already initialized`
- ❌ `WebView environment initialization failed`
- ❌ `WebView is not available on this device` (除非设备真的不支持WebView)

取而代之的是：

- ✅ `WebView environment initialized successfully` (只出现一次)
- ✅ `WebView environment already initialized, skipping...` (重复调用时)
- ✅ `WebView created and configured successfully` (WebView创建成功时)
